import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  List,
  Button,
  TextInput,
  Switch,
  Divider,
  Avatar,
  Text,
  Surface,
  useTheme,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { colors, spacing, typography } from '../../constants';

interface ProfileForm {
  fullName: string;
  phone: string;
}

interface NotificationSettings {
  newBookings: boolean;
  cancellations: boolean;
  checkIns: boolean;
  checkOuts: boolean;
  maintenance: boolean;
  reports: boolean;
}

export const AdminProfileScreen = ({ navigation }: any) => {
  const { user, signOut, updateProfile } = useAuthStore();
  const theme = useTheme();
  
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<ProfileForm>({
    fullName: user?.full_name || '',
    phone: user?.phone || '',
  });
  
  const [notifications, setNotifications] = useState<NotificationSettings>({
    newBookings: true,
    cancellations: true,
    checkIns: true,
    checkOuts: true,
    maintenance: true,
    reports: false,
  });

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSaveProfile = async () => {
    if (!form.fullName.trim()) {
      Alert.alert('Error', 'Full name is required');
      return;
    }

    setLoading(true);
    try {
      const result = await updateProfile({
        full_name: form.fullName.trim(),
        phone: form.phone.trim() || undefined,
      });

      if (result.success) {
        setEditing(false);
        Alert.alert('Success', 'Profile updated successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to update profile');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => signOut(),
        },
      ]
    );
  };

  if (!user) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Surface style={styles.headerCard}>
          <View style={styles.profileHeader}>
            <Avatar.Text
              size={80}
              label={getInitials(user.full_name)}
              style={[styles.avatar, { backgroundColor: colors.primary }]}
            />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{user.full_name}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>
              <Text style={styles.userRole}>{user.role.toUpperCase()}</Text>
            </View>
          </View>
        </Surface>

        {/* Profile Information */}
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <Title style={styles.sectionTitle}>Profile Information</Title>
              <Button
                mode={editing ? 'contained' : 'outlined'}
                onPress={() => editing ? handleSaveProfile() : setEditing(true)}
                loading={loading}
                disabled={loading}
                compact
              >
                {editing ? 'Save' : 'Edit'}
              </Button>
            </View>

            {editing ? (
              <View style={styles.formContainer}>
                <TextInput
                  label="Full Name"
                  value={form.fullName}
                  onChangeText={(text) => setForm(prev => ({ ...prev, fullName: text }))}
                  style={styles.input}
                  mode="outlined"
                />
                <TextInput
                  label="Phone Number"
                  value={form.phone}
                  onChangeText={(text) => setForm(prev => ({ ...prev, phone: text }))}
                  style={styles.input}
                  mode="outlined"
                  keyboardType="phone-pad"
                />
                <Button
                  mode="text"
                  onPress={() => {
                    setEditing(false);
                    setForm({
                      fullName: user.full_name,
                      phone: user.phone || '',
                    });
                  }}
                  style={styles.cancelButton}
                >
                  Cancel
                </Button>
              </View>
            ) : (
              <View>
                <List.Item
                  title="Full Name"
                  description={user.full_name}
                  left={() => <MaterialIcons name="person" size={24} color={colors.primary} />}
                />
                <List.Item
                  title="Email"
                  description={user.email}
                  left={() => <MaterialIcons name="email" size={24} color={colors.primary} />}
                />
                <List.Item
                  title="Phone"
                  description={user.phone || 'Not provided'}
                  left={() => <MaterialIcons name="phone" size={24} color={colors.primary} />}
                />
                <List.Item
                  title="Role"
                  description={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  left={() => <MaterialIcons name="admin-panel-settings" size={24} color={colors.primary} />}
                />
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Notification Settings */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Notification Preferences</Title>
            <List.Item
              title="New Bookings"
              description="Get notified when new reservations are made"
              right={() => (
                <Switch
                  value={notifications.newBookings}
                  onValueChange={(value) => 
                    setNotifications(prev => ({ ...prev, newBookings: value }))
                  }
                />
              )}
            />
            <List.Item
              title="Cancellations"
              description="Get notified when reservations are cancelled"
              right={() => (
                <Switch
                  value={notifications.cancellations}
                  onValueChange={(value) => 
                    setNotifications(prev => ({ ...prev, cancellations: value }))
                  }
                />
              )}
            />
            <List.Item
              title="Check-ins & Check-outs"
              description="Get notified about daily check-ins and check-outs"
              right={() => (
                <Switch
                  value={notifications.checkIns}
                  onValueChange={(value) => 
                    setNotifications(prev => ({ ...prev, checkIns: value }))
                  }
                />
              )}
            />
            <List.Item
              title="Maintenance Alerts"
              description="Get notified about room maintenance issues"
              right={() => (
                <Switch
                  value={notifications.maintenance}
                  onValueChange={(value) => 
                    setNotifications(prev => ({ ...prev, maintenance: value }))
                  }
                />
              )}
            />
            <List.Item
              title="Weekly Reports"
              description="Receive weekly analytics and performance reports"
              right={() => (
                <Switch
                  value={notifications.reports}
                  onValueChange={(value) => 
                    setNotifications(prev => ({ ...prev, reports: value }))
                  }
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Account Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Account</Title>
            <List.Item
              title="Change Password"
              description="Update your account password"
              left={() => <MaterialIcons name="lock" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={() => {
                // TODO: Implement password change functionality
                Alert.alert('Coming Soon', 'Password change functionality will be available soon.');
              }}
            />
            <Divider style={styles.divider} />
            <List.Item
              title="Sign Out"
              description="Sign out of your admin account"
              left={() => <MaterialIcons name="logout" size={24} color={colors.error} />}
              titleStyle={{ color: colors.error }}
              onPress={handleSignOut}
            />
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  headerCard: {
    margin: spacing.md,
    borderRadius: 12,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
  },
  avatar: {
    marginRight: spacing.md,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: colors.onSurfaceVariant,
    marginBottom: 4,
  },
  userRole: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    backgroundColor: colors.primaryContainer,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  card: {
    margin: spacing.md,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.onSurface,
  },
  formContainer: {
    gap: spacing.md,
  },
  input: {
    backgroundColor: colors.surface,
  },
  cancelButton: {
    alignSelf: 'flex-start',
  },
  divider: {
    marginVertical: spacing.sm,
  },
});
